function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import LaptopOutlinedSvg from "@ant-design/icons-svg/es/asn/LaptopOutlined";
import AntdIcon from "../components/AntdIcon";
const LaptopOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: LaptopOutlinedSvg
}));

/**![laptop](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTk1Ni45IDg0NS4xTDg5Ni40IDYzMlYxNjhjMC0xNy43LTE0LjMtMzItMzItMzJoLTcwNGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NDY0TDY3LjkgODQ1LjFDNjAuNCA4NjYgNzUuOCA4ODggOTggODg4aDgyOC44YzIyLjIgMCAzNy42LTIyIDMwLjEtNDIuOXpNMjAwLjQgMjA4aDYyNHYzOTVoLTYyNFYyMDh6bTIyOC4zIDYwOGw4LjEtMzdoMTUwLjNsOC4xIDM3SDQyOC43em0yMjQgMGwtMTkuMS04Ni43Yy0uOC0zLjctNC4xLTYuMy03LjgtNi4zSDM5OC4yYy0zLjggMC03IDIuNi03LjggNi4zTDM3MS4zIDgxNkgxNTFsNDIuMy0xNDloNjM4LjJsNDIuMyAxNDlINjUyLjd6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(LaptopOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LaptopOutlined';
}
export default RefIcon;