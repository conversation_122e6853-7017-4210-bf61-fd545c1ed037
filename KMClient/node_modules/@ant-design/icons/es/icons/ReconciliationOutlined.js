function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ReconciliationOutlinedSvg from "@ant-design/icons-svg/es/asn/ReconciliationOutlined";
import AntdIcon from "../components/AntdIcon";
const ReconciliationOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: ReconciliationOutlinedSvg
}));

/**![reconciliation](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY3NiA1NjVjLTUwLjggMC05MiA0MS4yLTkyIDkyczQxLjIgOTIgOTIgOTIgOTItNDEuMiA5Mi05Mi00MS4yLTkyLTkyLTkyem0wIDEyNmMtMTguOCAwLTM0LTE1LjItMzQtMzRzMTUuMi0zNCAzNC0zNCAzNCAxNS4yIDM0IDM0LTE1LjIgMzQtMzQgMzR6bTIwNC01MjNINjY4YzAtMzAuOS0yNS4xLTU2LTU2LTU2aC04MGMtMzAuOSAwLTU2IDI1LjEtNTYgNTZIMjY0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnYyMDBoLTg4Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY0NDhjMCAxNy43IDE0LjMgMzIgMzIgMzJoMzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMydi0xNmgzNjhjMTcuNyAwIDMyLTE0LjMgMzItMzJWMjAwYzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNDEyIDY0aDcydi01Nmg2NHY1Nmg3MnY0OEg0Njh2LTQ4em0tMjAgNjE2SDE3NlY2MTZoMjcydjIzMnptMC0yOTZIMTc2di04OGgyNzJ2ODh6bTM5MiAyNDBINTEyVjQzMmMwLTE3LjctMTQuMy0zMi0zMi0zMkgzMDRWMjQwaDEwMHYxMDRoMzM2VjI0MGgxMDB2NTUyek03MDQgNDA4djk2YzAgNC40IDMuNiA4IDggOGg0OGM0LjQgMCA4LTMuNiA4LTh2LTk2YzAtNC40LTMuNi04LTgtOGgtNDhjLTQuNCAwLTggMy42LTggOHpNNTkyIDUxMmg0OGM0LjQgMCA4LTMuNiA4LTh2LTU2YzAtNC40LTMuNi04LTgtOGgtNDhjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDh6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(ReconciliationOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ReconciliationOutlined';
}
export default RefIcon;