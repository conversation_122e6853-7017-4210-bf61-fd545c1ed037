function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SlidersFilledSvg from "@ant-design/icons-svg/es/asn/SlidersFilled";
import AntdIcon from "../components/AntdIcon";
const SlidersFilled = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: SlidersFilledSvg
}));

/**![sliders](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkwNCAyOTZoLTY2di05NmMwLTQuNC0zLjYtOC04LThoLTUyYy00LjQgMC04IDMuNi04IDh2OTZoLTY2Yy00LjQgMC04IDMuNi04IDh2NDE2YzAgNC40IDMuNiA4IDggOGg2NnY5NmMwIDQuNCAzLjYgOCA4IDhoNTJjNC40IDAgOC0zLjYgOC04di05Nmg2NmM0LjQgMCA4LTMuNiA4LThWMzA0YzAtNC40LTMuNi04LTgtOHptLTU4NC03MmgtNjZ2LTU2YzAtNC40LTMuNi04LTgtOGgtNTJjLTQuNCAwLTggMy42LTggOHY1NmgtNjZjLTQuNCAwLTggMy42LTggOHY1NjBjMCA0LjQgMy42IDggOCA4aDY2djU2YzAgNC40IDMuNiA4IDggOGg1MmM0LjQgMCA4LTMuNiA4LTh2LTU2aDY2YzQuNCAwIDgtMy42IDgtOFYyMzJjMC00LjQtMy42LTgtOC04em0yOTIgMTgwaC02NlYyMzJjMC00LjQtMy42LTgtOC04aC01MmMtNC40IDAtOCAzLjYtOCA4djE3MmgtNjZjLTQuNCAwLTggMy42LTggOHYyMDBjMCA0LjQgMy42IDggOCA4aDY2djE3MmMwIDQuNCAzLjYgOCA4IDhoNTJjNC40IDAgOC0zLjYgOC04VjYyMGg2NmM0LjQgMCA4LTMuNiA4LThWNDEyYzAtNC40LTMuNi04LTgtOHoiIC8+PC9zdmc+) */
const RefIcon = /*#__PURE__*/React.forwardRef(SlidersFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SlidersFilled';
}
export default RefIcon;