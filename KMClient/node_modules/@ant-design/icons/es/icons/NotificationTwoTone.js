function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import NotificationTwoToneSvg from "@ant-design/icons-svg/es/asn/NotificationTwoTone";
import AntdIcon from "../components/AntdIcon";
const NotificationTwoTone = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: NotificationTwoToneSvg
}));

/**![notification](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTIyOS42IDY3OC4xYy0zLjcgMTEuNi01LjYgMjMuOS01LjYgMzYuNCAwLTEyLjUgMi0yNC44IDUuNy0zNi40aC0uMXptNzYuMy0yNjAuMkgxODR2MTg4LjJoMTIxLjlsMTIuOSA1LjJMODQwIDgyMC43VjIwMy4zTDMxOC44IDQxMi43eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODgwIDExMmMtMy44IDAtNy43LjctMTEuNiAyLjNMMjkyIDM0NS45SDEyOGMtOC44IDAtMTYgNy40LTE2IDE2LjZ2Mjk5YzAgOS4yIDcuMiAxNi42IDE2IDE2LjZoMTAxLjdjLTMuNyAxMS42LTUuNyAyMy45LTUuNyAzNi40IDAgNjUuOSA1My44IDExOS41IDEyMCAxMTkuNSA1NS40IDAgMTAyLjEtMzcuNiAxMTUuOS04OC40bDQwOC42IDE2NC4yYzMuOSAxLjUgNy44IDIuMyAxMS42IDIuMyAxNi45IDAgMzItMTQuMiAzMi0zMy4yVjE0NS4yQzkxMiAxMjYuMiA4OTcgMTEyIDg4MCAxMTJ6TTM0NCA3NjIuM2MtMjYuNSAwLTQ4LTIxLjQtNDgtNDcuOCAwLTExLjIgMy45LTIxLjkgMTEtMzAuNGw4NC45IDM0LjFjLTIgMjQuNi0yMi43IDQ0LjEtNDcuOSA0NC4xem00OTYgNTguNEwzMTguOCA2MTEuM2wtMTIuOS01LjJIMTg0VjQxNy45aDEyMS45bDEyLjktNS4yTDg0MCAyMDMuM3Y2MTcuNHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
const RefIcon = /*#__PURE__*/React.forwardRef(NotificationTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'NotificationTwoTone';
}
export default RefIcon;