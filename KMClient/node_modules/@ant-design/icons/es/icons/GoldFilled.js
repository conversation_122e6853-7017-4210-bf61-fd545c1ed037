function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import GoldFilledSvg from "@ant-design/icons-svg/es/asn/GoldFilled";
import AntdIcon from "../components/AntdIcon";
const GoldFilled = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: GoldFilledSvg
}));

/**![gold](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkwNS45IDgwNi43bC00MC4yLTI0OGMtLjYtMy45LTQtNi43LTcuOS02LjdINTk2LjJjLTMuOSAwLTcuMyAyLjgtNy45IDYuN2wtNDAuMiAyNDhjLS4xLjQtLjEuOS0uMSAxLjMgMCA0LjQgMy42IDggOCA4aDM0MmMuNCAwIC45IDAgMS4zLS4xIDQuMy0uNyA3LjMtNC44IDYuNi05LjJ6bS00NzAuMi0yNDhjLS42LTMuOS00LTYuNy03LjktNi43SDE2Ni4yYy0zLjkgMC03LjMgMi44LTcuOSA2LjdsLTQwLjIgMjQ4Yy0uMS40LS4xLjktLjEgMS4zIDAgNC40IDMuNiA4IDggOGgzNDJjLjQgMCAuOSAwIDEuMy0uMSA0LjQtLjcgNy4zLTQuOCA2LjYtOS4ybC00MC4yLTI0OHpNMzQyIDQ3MmgzNDJjLjQgMCAuOSAwIDEuMy0uMSA0LjQtLjcgNy4zLTQuOCA2LjYtOS4ybC00MC4yLTI0OGMtLjYtMy45LTQtNi43LTcuOS02LjdIMzgyLjJjLTMuOSAwLTcuMyAyLjgtNy45IDYuN2wtNDAuMiAyNDhjLS4xLjQtLjEuOS0uMSAxLjMgMCA0LjQgMy42IDggOCA4eiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(GoldFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'GoldFilled';
}
export default RefIcon;