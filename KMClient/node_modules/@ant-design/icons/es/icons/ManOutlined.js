function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ManOutlinedSvg from "@ant-design/icons-svg/es/asn/ManOutlined";
import AntdIcon from "../components/AntdIcon";
const ManOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: ManOutlinedSvg
}));

/**![man](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg3NCAxMjBINjIyYy0zLjMgMC02IDIuNy02IDZ2NTZjMCAzLjMgMi43IDYgNiA2aDE2MC40TDU4My4xIDM4Ny4zYy01MC0zOC41LTExMS01OS4zLTE3NS4xLTU5LjMtNzYuOSAwLTE0OS4zIDMwLTIwMy42IDg0LjRTMTIwIDUzOS4xIDEyMCA2MTZzMzAgMTQ5LjMgODQuNCAyMDMuNkMyNTguNyA4NzQgMzMxLjEgOTA0IDQwOCA5MDRzMTQ5LjMtMzAgMjAzLjYtODQuNEM2NjYgNzY1LjMgNjk2IDY5Mi45IDY5NiA2MTZjMC02NC4xLTIwLjgtMTI0LjktNTkuMi0xNzQuOUw4MzYgMjQxLjlWNDAyYzAgMy4zIDIuNyA2IDYgNmg1NmMzLjMgMCA2LTIuNyA2LTZWMTUwYzAtMTYuNS0xMy41LTMwLTMwLTMwek00MDggODI4Yy0xMTYuOSAwLTIxMi05NS4xLTIxMi0yMTJzOTUuMS0yMTIgMjEyLTIxMiAyMTIgOTUuMSAyMTIgMjEyLTk1LjEgMjEyLTIxMiAyMTJ6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(ManOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ManOutlined';
}
export default RefIcon;