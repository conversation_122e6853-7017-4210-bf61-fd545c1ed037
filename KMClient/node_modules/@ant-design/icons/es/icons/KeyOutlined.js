function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import KeyOutlinedSvg from "@ant-design/icons-svg/es/asn/KeyOutlined";
import AntdIcon from "../components/AntdIcon";
const KeyOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: KeyOutlinedSvg
}));

/**![key](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTYwOCAxMTJjLTE2Ny45IDAtMzA0IDEzNi4xLTMwNCAzMDQgMCA3MC4zIDIzLjkgMTM1IDYzLjkgMTg2LjVsLTQxLjEgNDEuMS02Mi4zLTYyLjNhOC4xNSA4LjE1IDAgMDAtMTEuNCAwbC0zOS44IDM5LjhhOC4xNSA4LjE1IDAgMDAwIDExLjRsNjIuMyA2Mi4zLTQ0LjkgNDQuOS02Mi4zLTYyLjNhOC4xNSA4LjE1IDAgMDAtMTEuNCAwbC0zOS44IDM5LjhhOC4xNSA4LjE1IDAgMDAwIDExLjRsNjIuMyA2Mi4zLTY1LjMgNjUuM2E4LjAzIDguMDMgMCAwMDAgMTEuM2w0Mi4zIDQyLjNjMy4xIDMuMSA4LjIgMy4xIDExLjMgMGwyNTMuNi0yNTMuNkEzMDQuMDYgMzA0LjA2IDAgMDA2MDggNzIwYzE2Ny45IDAgMzA0LTEzNi4xIDMwNC0zMDRTNzc1LjkgMTEyIDYwOCAxMTJ6bTE2MS4yIDQ2NS4yQzcyNi4yIDYyMC4zIDY2OC45IDY0NCA2MDggNjQ0Yy02MC45IDAtMTE4LjItMjMuNy0xNjEuMi02Ni44LTQzLjEtNDMtNjYuOC0xMDAuMy02Ni44LTE2MS4yIDAtNjAuOSAyMy43LTExOC4yIDY2LjgtMTYxLjIgNDMtNDMuMSAxMDAuMy02Ni44IDE2MS4yLTY2LjggNjAuOSAwIDExOC4yIDIzLjcgMTYxLjIgNjYuOCA0My4xIDQzIDY2LjggMTAwLjMgNjYuOCAxNjEuMiAwIDYwLjktMjMuNyAxMTguMi02Ni44IDE2MS4yeiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(KeyOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'KeyOutlined';
}
export default RefIcon;