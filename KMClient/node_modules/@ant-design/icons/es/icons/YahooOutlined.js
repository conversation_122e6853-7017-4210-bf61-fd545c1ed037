function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import YahooOutlinedSvg from "@ant-design/icons-svg/es/asn/YahooOutlined";
import AntdIcon from "../components/AntdIcon";
const YahooOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: YahooOutlinedSvg
}));

/**![yahoo](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1OS45IDY4MS40aC0xNC4xYy0yNy4xIDAtNDkuMiAyMi4yLTQ5LjIgNDkuM3YxNC4xYzAgMjcuMSAyMi4yIDQ5LjMgNDkuMiA0OS4zaDE0LjFjMjcuMSAwIDQ5LjItMjIuMiA0OS4yLTQ5LjN2LTE0LjFjMC0yNy4xLTIyLjItNDkuMy00OS4yLTQ5LjN6TTQwMi42IDIzMUMyMTYuMiAyMzEgNjUgMzU3IDY1IDUxMi41UzIxNi4yIDc5NCA0MDIuNiA3OTRzMzM3LjYtMTI2IDMzNy42LTI4MS41UzU4OS4xIDIzMSA0MDIuNiAyMzF6bTAgNTA3QzI0NS4xIDczOCAxMjEgNjM0LjYgMTIxIDUxMi41YzAtNjIuMyAzMi4zLTExOS43IDg0LjktMTYxdjQ4LjRoMzdsMTU5LjggMTU5Ljl2NjUuM2gtODQuNHY1Ni4zaDIyNS4xdi01Ni4zSDQ1OXYtNjUuM2wxMDMuNS0xMDMuNmg2NS4zdi01Ni4zSDQ1OXY2NS4zbC0yOC4xIDI4LjEtOTMuNC05My41aDM3di01Ni4zSDIxNi40YzQ5LjQtMzUgMTE0LjMtNTYuNiAxODYuMi01Ni42IDE1Ny42IDAgMjgxLjYgMTAzLjQgMjgxLjYgMjI1LjVTNTYwLjIgNzM4IDQwMi42IDczOHptNTM0LjctNTA3SDgyNC43Yy0xNS41IDAtMjcuNyAxMi42LTI3LjEgMjguMWwxMy4xIDM2Nmg4NC40bDY1LjQtMzY2LjRjMi43LTE1LjItNy44LTI3LjctMjMuMi0yNy43eiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(YahooOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'YahooOutlined';
}
export default RefIcon;