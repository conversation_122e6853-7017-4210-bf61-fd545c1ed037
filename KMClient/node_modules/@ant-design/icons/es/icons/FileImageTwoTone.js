function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FileImageTwoToneSvg from "@ant-design/icons-svg/es/asn/FileImageTwoTone";
import AntdIcon from "../components/AntdIcon";
const FileImageTwoTone = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: FileImageTwoToneSvg
}));

/**![file-image](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUzNCAzNTJWMTM2SDIzMnY3NTJoNTYwVjM5NEg1NzZhNDIgNDIgMCAwMS00Mi00MnptLTEzNCA1MGMyMi4xIDAgNDAgMTcuOSA0MCA0MHMtMTcuOSA0MC00MCA0MC00MC0xNy45LTQwLTQwIDE3LjktNDAgNDAtNDB6bTI5NiAyOTRIMzI4LjFjLTYuNyAwLTEwLjQtNy43LTYuMy0xMi45bDk5LjgtMTI3LjJhOCA4IDAgMDExMi42IDBsNDEuMSA1Mi40IDc3LjgtOTkuMmE4LjEgOC4xIDAgMDExMi43IDBsMTM2LjUgMTc0YzQuMSA1LjIuNCAxMi45LTYuMyAxMi45eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODU0LjYgMjg4LjZMNjM5LjQgNzMuNGMtNi02LTE0LjEtOS40LTIyLjYtOS40SDE5MmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2ODMyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDY0MGMxNy43IDAgMzItMTQuMyAzMi0zMlYzMTEuM2MwLTguNS0zLjQtMTYuNy05LjQtMjIuN3pNNjAyIDEzNy44TDc5MC4yIDMyNkg2MDJWMTM3Ljh6TTc5MiA4ODhIMjMyVjEzNmgzMDJ2MjE2YTQyIDQyIDAgMDA0MiA0MmgyMTZ2NDk0eiIgZmlsbD0iIzE2NzdmZiIgLz48cGF0aCBkPSJNNTUzLjEgNTA5LjFsLTc3LjggOTkuMi00MS4xLTUyLjRhOCA4IDAgMDAtMTIuNiAwbC05OS44IDEyNy4yYTcuOTggNy45OCAwIDAwNi4zIDEyLjlINjk2YzYuNyAwIDEwLjQtNy43IDYuMy0xMi45bC0xMzYuNS0xNzRhOC4xIDguMSAwIDAwLTEyLjcgMHpNMzYwIDQ0MmE0MCA0MCAwIDEwODAgMCA0MCA0MCAwIDEwLTgwIDB6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(FileImageTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FileImageTwoTone';
}
export default RefIcon;