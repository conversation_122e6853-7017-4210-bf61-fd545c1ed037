function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FileUnknownFilledSvg from "@ant-design/icons-svg/es/asn/FileUnknownFilled";
import AntdIcon from "../components/AntdIcon";
const FileUnknownFilled = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: FileUnknownFilledSvg
}));

/**![file-unknown](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NC42IDI4OC43YzYgNiA5LjQgMTQuMSA5LjQgMjIuNlY5MjhjMCAxNy43LTE0LjMgMzItMzIgMzJIMTkyYy0xNy43IDAtMzItMTQuMy0zMi0zMlY5NmMwLTE3LjcgMTQuMy0zMiAzMi0zMmg0MjQuN2M4LjUgMCAxNi43IDMuNCAyMi43IDkuNGwyMTUuMiAyMTUuM3pNNzkwLjIgMzI2TDYwMiAxMzcuOFYzMjZoMTg4LjJ6TTQwMiA1NDljMCA1LjQgNC40IDkuNSA5LjggOS41aDMyLjRjNS40IDAgOS44LTQuMiA5LjgtOS40IDAtMjguMiAyNS44LTUxLjYgNTgtNTEuNnM1OCAyMy40IDU4IDUxLjVjMCAyNS4zLTIxIDQ3LjItNDkuMyA1MC45LTE5LjMgMi44LTM0LjUgMjAuMy0zNC43IDQwLjF2MzJjMCA1LjUgNC41IDEwIDEwIDEwaDMyYzUuNSAwIDEwLTQuNSAxMC0xMHYtMTIuMmMwLTYgNC0xMS41IDkuNy0xMy4zIDQ0LjYtMTQuNCA3NS01NCA3NC4zLTk4LjktLjgtNTUuNS00OS4yLTEwMC44LTEwOC41LTEwMS42LTYxLjQtLjctMTExLjUgNDUuNi0xMTEuNSAxMDN6bTExMCAyMjdhMzIgMzIgMCAxMDAtNjQgMzIgMzIgMCAwMDAgNjR6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(FileUnknownFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FileUnknownFilled';
}
export default RefIcon;