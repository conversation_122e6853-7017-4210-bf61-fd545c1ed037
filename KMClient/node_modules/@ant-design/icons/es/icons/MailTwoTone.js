function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import MailTwoToneSvg from "@ant-design/icons-svg/es/asn/MailTwoTone";
import AntdIcon from "../components/AntdIcon";
const MailTwoTone = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: MailTwoToneSvg
}));

/**![mail](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ3Ny41IDUzNi4zTDEzNS45IDI3MC43bC0yNy41LTIxLjQgMjcuNiAyMS41Vjc5Mmg3NTJWMjcwLjhMNTQ2LjIgNTM2LjNhNTUuOTkgNTUuOTkgMCAwMS02OC43IDB6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik04NzYuMyAxOTguOGwzOS4zIDUwLjUtMjcuNiAyMS41IDI3LjctMjEuNS0zOS4zLTUwLjV6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik05MjggMTYwSDk2Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY2NDBjMCAxNy43IDE0LjMgMzIgMzIgMzJoODMyYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE5MmMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTk0LjUgNzIuMUw1MTIgNDgyIDE5MC41IDIzMi4xaDY0M3ptNTQuNSAzOC43Vjc5MkgxMzZWMjcwLjhsLTI3LjYtMjEuNSAyNy41IDIxLjQgMzQxLjYgMjY1LjZhNTUuOTkgNTUuOTkgMCAwMDY4LjcgMEw4ODggMjcwLjhsMjcuNi0yMS41LTM5LjMtNTAuNWguMWwzOS4zIDUwLjUtMjcuNyAyMS41eiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(MailTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MailTwoTone';
}
export default RefIcon;