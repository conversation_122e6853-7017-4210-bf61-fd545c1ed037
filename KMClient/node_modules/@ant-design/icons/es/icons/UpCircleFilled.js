function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import UpCircleFilledSvg from "@ant-design/icons-svg/es/asn/UpCircleFilled";
import AntdIcon from "../components/AntdIcon";
const UpCircleFilled = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: UpCircleFilledSvg
}));

/**![up-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0xNzggNTU1aC00Ni45Yy0xMC4yIDAtMTkuOS00LjktMjUuOS0xMy4yTDUxMiA0NjAuNCA0MDYuOCA2MDUuOGMtNiA4LjMtMTUuNiAxMy4yLTI1LjkgMTMuMkgzMzRjLTYuNSAwLTEwLjMtNy40LTYuNS0xMi43bDE3OC0yNDZjMy4yLTQuNCA5LjctNC40IDEyLjkgMGwxNzggMjQ2YzMuOSA1LjMuMSAxMi43LTYuNCAxMi43eiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(UpCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UpCircleFilled';
}
export default RefIcon;