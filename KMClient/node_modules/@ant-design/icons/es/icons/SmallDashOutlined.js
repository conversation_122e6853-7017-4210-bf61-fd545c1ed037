function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SmallDashOutlinedSvg from "@ant-design/icons-svg/es/asn/SmallDashOutlined";
import AntdIcon from "../components/AntdIcon";
const SmallDashOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: SmallDashOutlinedSvg
}));

/**![small-dash](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTExMiA0NzZoNzJ2NzJoLTcyem0xODIgMGg3MnY3MmgtNzJ6bTM2NCAwaDcydjcyaC03MnptMTgyIDBoNzJ2NzJoLTcyem0tMzY0IDBoNzJ2NzJoLTcyeiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(SmallDashOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SmallDashOutlined';
}
export default RefIcon;