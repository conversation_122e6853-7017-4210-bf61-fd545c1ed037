function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import PicRightOutlinedSvg from "@ant-design/icons-svg/es/asn/PicRightOutlined";
import AntdIcon from "../components/AntdIcon";
const PicRightOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: PicRightOutlinedSvg
}));

/**![pic-right](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTk1MiA3OTJINzJjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDhoODgwYzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04em0wLTYzMkg3MmMtNC40IDAtOCAzLjYtOCA4djU2YzAgNC40IDMuNiA4IDggOGg4ODBjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6bS0yNCA1MDBjOC44IDAgMTYtNy4yIDE2LTE2VjM4MGMwLTguOC03LjItMTYtMTYtMTZINDE2Yy04LjggMC0xNiA3LjItMTYgMTZ2MjY0YzAgOC44IDcuMiAxNiAxNiAxNmg1MTJ6TTQ3MiA0MzZoNDAwdjE1Mkg0NzJWNDM2ek04MCA2NDZjMCA0LjQgMy42IDggOCA4aDIyNGM0LjQgMCA4LTMuNiA4LTh2LTU2YzAtNC40LTMuNi04LTgtOEg4OGMtNC40IDAtOCAzLjYtOCA4djU2em04LTIwNGgyMjRjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LThIODhjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDh6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(PicRightOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PicRightOutlined';
}
export default RefIcon;