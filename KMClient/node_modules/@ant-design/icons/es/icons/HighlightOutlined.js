function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import HighlightOutlinedSvg from "@ant-design/icons-svg/es/asn/HighlightOutlined";
import AntdIcon from "../components/AntdIcon";
const HighlightOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: HighlightOutlinedSvg
}));

/**![highlight](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTk1Ny42IDUwNy40TDYwMy4yIDE1OC4yYTcuOSA3LjkgMCAwMC0xMS4yIDBMMzUzLjMgMzkzLjRhOC4wMyA4LjAzIDAgMDAtLjEgMTEuM2wuMS4xIDQwIDM5LjQtMTE3LjIgMTE1LjNhOC4wMyA4LjAzIDAgMDAtLjEgMTEuM2wuMS4xIDM5LjUgMzguOS0xODkuMSAxODdINzIuMWMtNC40IDAtOC4xIDMuNi04LjEgOFY4NjBjMCA0LjQgMy42IDggOCA4aDM0NC45YzIuMSAwIDQuMS0uOCA1LjYtMi4zbDc2LjEtNzUuNiA0MC40IDM5LjhhNy45IDcuOSAwIDAwMTEuMiAwbDExNy4xLTExNS42IDQwLjEgMzkuNWE3LjkgNy45IDAgMDAxMS4yIDBsMjM4LjctMjM1LjJjMy40LTMgMy40LTggLjMtMTEuMnpNMzg5LjggNzk2LjJIMjI5LjZsMTM0LjQtMTMzIDgwLjEgNzguOS01NC4zIDU0LjF6bTE1NC44LTYyLjFMMzczLjIgNTY1LjJsNjguNi02Ny42IDE3MS40IDE2OC45LTY4LjYgNjcuNnpNNzEzLjEgNjU4TDQ1MC4zIDM5OS4xIDU5Ny42IDI1NGwyNjIuOCAyNTktMTQ3LjMgMTQ1eiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(HighlightOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'HighlightOutlined';
}
export default RefIcon;