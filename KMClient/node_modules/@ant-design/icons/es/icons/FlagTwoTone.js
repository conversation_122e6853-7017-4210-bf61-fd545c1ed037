function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FlagTwoToneSvg from "@ant-design/icons-svg/es/asn/FlagTwoTone";
import AntdIcon from "../components/AntdIcon";
const FlagTwoTone = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: FlagTwoToneSvg
}));

/**![flag](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE4NCAyMzJoMzY4djMzNkgxODR6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik02MjQgNjMyYzAgNC40LTMuNiA4LTggOEg1MDR2NzNoMzM2VjM3N0g2MjR2MjU1eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODgwIDMwNUg2MjRWMTkyYzAtMTcuNy0xNC4zLTMyLTMyLTMySDE4NHYtNDBjMC00LjQtMy42LTgtOC04aC01NmMtNC40IDAtOCAzLjYtOCA4djc4NGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04VjY0MGgyNDh2MTEzYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDQxNmMxNy43IDAgMzItMTQuMyAzMi0zMlYzMzdjMC0xNy43LTE0LjMtMzItMzItMzJ6TTE4NCA1NjhWMjMyaDM2OHYzMzZIMTg0em02NTYgMTQ1SDUwNHYtNzNoMTEyYzQuNCAwIDgtMy42IDgtOFYzNzdoMjE2djMzNnoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
const RefIcon = /*#__PURE__*/React.forwardRef(FlagTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FlagTwoTone';
}
export default RefIcon;