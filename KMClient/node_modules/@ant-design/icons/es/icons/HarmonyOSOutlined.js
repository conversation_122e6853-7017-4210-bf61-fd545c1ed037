function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import HarmonyOSOutlinedSvg from "@ant-design/icons-svg/es/asn/HarmonyOSOutlined";
import AntdIcon from "../components/AntdIcon";
const HarmonyOSOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: HarmonyOSOutlinedSvg
}));

/**![harmony-o-s](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNTExLjUgNjVDNzE5Ljk5IDY1IDg4OSAyMzQuMDEgODg5IDQ0Mi41UzcxOS45OSA4MjAgNTExLjUgODIwIDEzNCA2NTAuOTkgMTM0IDQ0Mi41IDMwMy4wMSA2NSA1MTEuNSA2NW0wIDY0QzMzOC4zNiAxMjkgMTk4IDI2OS4zNiAxOTggNDQyLjVTMzM4LjM2IDc1NiA1MTEuNSA3NTYgODI1IDYxNS42NCA4MjUgNDQyLjUgNjg0LjY0IDEyOSA1MTEuNSAxMjlNNzQ1IDg4OXY3MkgyNzh2LTcyeiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(HarmonyOSOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'HarmonyOSOutlined';
}
export default RefIcon;