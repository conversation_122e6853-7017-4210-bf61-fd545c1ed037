function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import PinterestFilledSvg from "@ant-design/icons-svg/es/asn/PinterestFilled";
import AntdIcon from "../components/AntdIcon";
const PinterestFilled = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: PinterestFilledSvg
}));

/**![pinterest](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNTEyIDY0QzI2NC45NyA2NCA2NCAyNjQuOTcgNjQgNTEyYzAgMTkyLjUzIDEyMi4wOCAzNTcuMDQgMjkyLjg4IDQyMC4yOC00LjkyLTQzLjg2LTQuMTQtMTE1LjY4IDMuOTctMTUwLjQ2IDcuNi0zMi42NiA0OS4xMS0yMDguMTYgNDkuMTEtMjA4LjE2cy0xMi41My0yNS4xLTEyLjUzLTYyLjE2YzAtNTguMjQgMzMuNzQtMTAxLjcgNzUuNzctMTAxLjcgMzUuNzQgMCA1Mi45NyAyNi44MyA1Mi45NyA1OC45OCAwIDM1Ljk2LTIyLjg1IDg5LjY2LTM0LjcgMTM5LjQzLTkuODcgNDEuNyAyMC45MSA3NS43IDYyLjAyIDc1LjcgNzQuNDMgMCAxMzEuNjQtNzguNSAxMzEuNjQtMTkxLjc3IDAtMTAwLjI3LTcyLjAzLTE3MC4zOC0xNzQuOS0xNzAuMzgtMTE5LjE1IDAtMTg5LjA4IDg5LjM4LTE4OS4wOCAxODEuNzUgMCAzNS45OCAxMy44NSA3NC41OCAzMS4xNiA5NS41OCAzLjQyIDQuMTYgMy45MiA3Ljc4IDIuOSAxMi0zLjE3IDEzLjIyLTEwLjIyIDQxLjY3LTExLjYzIDQ3LjUtMS44MiA3LjY4LTYuMDcgOS4yOC0xNCA1LjU5LTUyLjMtMjQuMzYtODUtMTAwLjgxLTg1LTE2Mi4yNSAwLTEzMi4xIDk1Ljk2LTI1My40MyAyNzYuNzEtMjUzLjQzIDE0NS4yOSAwIDI1OC4xOCAxMDMuNSAyNTguMTggMjQxLjg4IDAgMTQ0LjM0LTkxLjAyIDI2MC40OS0yMTcuMzEgMjYwLjQ5LTQyLjQ0IDAtODIuMzMtMjIuMDUtOTUuOTctNDguMSAwIDAtMjEgNzkuOTYtMjYuMSA5OS41Ni04LjgyIDMzLjktNDYuNTUgMTA0LjEzLTY1LjQ5IDEzNi4wM0E0NDYuMTYgNDQ2LjE2IDAgMDA1MTIgOTYwYzI0Ny4wNCAwIDQ0OC0yMDAuOTcgNDQ4LTQ0OFM3NTkuMDQgNjQgNTEyIDY0IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(PinterestFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PinterestFilled';
}
export default RefIcon;