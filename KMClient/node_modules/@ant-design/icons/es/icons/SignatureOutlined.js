function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SignatureOutlinedSvg from "@ant-design/icons-svg/es/asn/SignatureOutlined";
import AntdIcon from "../components/AntdIcon";
const SignatureOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: SignatureOutlinedSvg
}));

/**![signature](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTQ1LjcxIDc1MmMyIDAgNC0uMiA1Ljk4LS41TDMxOS45IDcyMmMxLjk5LS40IDMuODgtMS4zIDUuMjgtMi44bDQyMy45MS00MjMuODdhOS45MyA5LjkzIDAgMDAwLTE0LjA2TDU4Mi44OCAxMTQuOUM1ODEgMTEzIDU3OC41IDExMiA1NzUuODIgMTEycy01LjE4IDEtNy4wOCAyLjlMMTQ0LjgyIDUzOC43NmMtMS41IDEuNS0yLjQgMy4yOS0yLjggNS4yOGwtMjkuNSAxNjguMTdhMzMuNTkgMzMuNTkgMCAwMDkuMzcgMjkuODFjNi41OCA2LjQ4IDE0Ljk1IDkuOTcgMjMuODIgOS45N201MS43NS04NS40M2wxNS42NS04OC45MiAzNjIuNy0zNjIuNjcgNzMuMjggNzMuMjctMzYyLjcgMzYyLjY3em00MDEuMzctOTguNjRjMjcuNjktMTQuODEgNTcuMjktMjAuODUgODUuNTQtMTUuNTIgMzIuMzcgNi4xIDU5LjcyIDI2LjUzIDc4Ljk2IDU5LjQgMjkuOTcgNTEuMjIgMjEuNjQgMTAyLjM0LTE4LjQ4IDE0NC4yNi0xNy41OCAxOC4zNi00MS4wNyAzNS4wMS03MCA1MC4zbC0uMy4xNS44Ni4yNmExNDcuODggMTQ3Ljg4IDAgMDA0MS41NCA2LjJsMS4xNy4wMWM2MS4wNyAwIDEwMC45OC0yMi4xIDEyNS4yOC02Ny44N2EzNiAzNiAwIDAxNjMuNiAzMy43NkM4NjkuNyA4NDkuMSA4MDQuOSA4ODUgNzE4LjEyIDg4NWMtNDcuNjkgMC05MS45NC0xNS4wMy0xMjguMTktNDEuMzZsLTEuMDUtLjc4LTEuMzYuNDdjLTQ2LjE4IDE2LTk4Ljc0IDI5Ljk1LTE1NS4zNyA0MS45NGwtMi4yNC40N2ExOTMxLjEgMTkzMS4xIDAgMDEtMTM5LjE2IDIzLjk2IDM2IDM2IDAgMTEtOS41LTcxLjM4IDE4NjAuMSAxODYwLjEgMCAwMDEzMy44NC0yMy4wNGM0Mi44LTkgODMtMTkuMTMgMTE5LjM1LTMwLjM0bC4yNC0uMDgtLjQ0LS42OWMtMTYuNDYtMjYuNDUtMjUuODYtNTUuNDMtMjYuMTQtODMuMjR2LTEuM2MwLTQ5LjkgMzkuNTUtMTA0LjMyIDkwLjczLTEzMS43TTY3MSA2MjMuMTdjLTEwLjc0LTIuMDMtMjQuMS43LTM4LjIyIDguMjYtMjkuNTUgMTUuOC01Mi43IDQ3LjY0LTUyLjcgNjguMiAwIDE4LjIgOC45IDQwLjE0IDI0LjcxIDU5LjczbC4yNC4zIDEuMjItLjUyYzM5LjE3LTE2LjU4IDY4LjQ5LTM0LjI3IDg1LjkzLTUyLjE4bC42NC0uNjdjMTguNzQtMTkuNTcgMjEuMzktMzUuODQgOC4zNi01OC4xLTkuMDYtMTUuNDctMTkuMDMtMjIuOTItMzAuMTgtMjUuMDIiIC8+PC9zdmc+) */
const RefIcon = /*#__PURE__*/React.forwardRef(SignatureOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SignatureOutlined';
}
export default RefIcon;