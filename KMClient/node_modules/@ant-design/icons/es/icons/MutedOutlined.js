function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import MutedOutlinedSvg from "@ant-design/icons-svg/es/asn/MutedOutlined";
import AntdIcon from "../components/AntdIcon";
const MutedOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: MutedOutlinedSvg
}));

/**![muted](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNzcxLjkxIDExNWEzMS42NSAzMS42NSAwIDAwLTE3LjQyIDUuMjdMNDAwIDM1MS45N0gyMzZhMTYgMTYgMCAwMC0xNiAxNnYyODguMDZhMTYgMTYgMCAwMDE2IDE2aDE2NGwzNTQuNSAyMzEuN2EzMS42NiAzMS42NiAwIDAwMTcuNDIgNS4yN2MxNi42NSAwIDMyLjA4LTEzLjI1IDMyLjA4LTMyLjA2VjE0Ny4wNmMwLTE4LjgtMTUuNDQtMzIuMDYtMzIuMDktMzIuMDZNNzMyIDIyMXY1ODJMNDM5LjM5IDYxMS43NWwtMTcuOTUtMTEuNzNIMjkyVjQyMy45OGgxMjkuNDRsMTcuOTUtMTEuNzN6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(MutedOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MutedOutlined';
}
export default RefIcon;