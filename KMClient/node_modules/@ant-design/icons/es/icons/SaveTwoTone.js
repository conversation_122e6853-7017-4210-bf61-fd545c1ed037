function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SaveTwoToneSvg from "@ant-design/icons-svg/es/asn/SaveTwoTone";
import AntdIcon from "../components/AntdIcon";
const SaveTwoTone = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: SaveTwoToneSvg
}));

/**![save](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTcwNCAzMjBjMCAxNy43LTE0LjMgMzItMzIgMzJIMzUyYy0xNy43IDAtMzItMTQuMy0zMi0zMlYxODRIMTg0djY1Nmg2NTZWMzQxLjhsLTEzNi0xMzZWMzIwek01MTIgNzMwYy03OS41IDAtMTQ0LTY0LjUtMTQ0LTE0NHM2NC41LTE0NCAxNDQtMTQ0IDE0NCA2NC41IDE0NCAxNDQtNjQuNSAxNDQtMTQ0IDE0NHoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTUxMiA0NDJjLTc5LjUgMC0xNDQgNjQuNS0xNDQgMTQ0czY0LjUgMTQ0IDE0NCAxNDQgMTQ0LTY0LjUgMTQ0LTE0NC02NC41LTE0NC0xNDQtMTQ0em0wIDIyNGMtNDQuMiAwLTgwLTM1LjgtODAtODBzMzUuOC04MCA4MC04MCA4MCAzNS44IDgwIDgwLTM1LjggODAtODAgODB6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik04OTMuMyAyOTMuM0w3MzAuNyAxMzAuN2MtLjctLjctMS40LTEuMy0yLjEtMi0uMS0uMS0uMy0uMi0uNC0uMy0uNy0uNy0xLjUtMS4zLTIuMi0xLjlhNjQgNjQgMCAwMC0yMi0xMS43VjExMkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjczNmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg3MzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzM4LjVjMC0xNy02LjctMzMuMi0xOC43LTQ1LjJ6TTM4NCAxODRoMjU2djEwNEgzODRWMTg0em00NTYgNjU2SDE4NFYxODRoMTM2djEzNmMwIDE3LjcgMTQuMyAzMiAzMiAzMmgzMjBjMTcuNyAwIDMyLTE0LjMgMzItMzJWMjA1LjhsMTM2IDEzNlY4NDB6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(SaveTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SaveTwoTone';
}
export default RefIcon;