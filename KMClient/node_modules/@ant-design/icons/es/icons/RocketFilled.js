function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import RocketFilledSvg from "@ant-design/icons-svg/es/asn/RocketFilled";
import AntdIcon from "../components/AntdIcon";
const RocketFilled = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: RocketFilledSvg
}));

/**![rocket](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2NCA3MzZjMC0xMTEuNi02NS40LTIwOC0xNjAtMjUyLjlWMzE3LjNjMC0xNS4xLTUuMy0yOS43LTE1LjEtNDEuMkw1MzYuNSA5NS40QzUzMC4xIDg3LjggNTIxIDg0IDUxMiA4NHMtMTguMSAzLjgtMjQuNSAxMS40TDMzNS4xIDI3Ni4xYTYzLjk3IDYzLjk3IDAgMDAtMTUuMSA0MS4ydjE2NS44QzIyNS40IDUyOCAxNjAgNjI0LjQgMTYwIDczNmgxNTYuNWMtMi4zIDcuMi0zLjUgMTUtMy41IDIzLjggMCAyMi4xIDcuNiA0My43IDIxLjQgNjAuOGE5Ny4yIDk3LjIgMCAwMDQzLjEgMzAuNmMyMy4xIDU0IDc1LjYgODguOCAxMzQuNSA4OC44IDI5LjEgMCA1Ny4zLTguNiA4MS40LTI0LjggMjMuNi0xNS44IDQxLjktMzcuOSA1My02NGE5NyA5NyAwIDAwNDMuMS0zMC41IDk3LjUyIDk3LjUyIDAgMDAyMS40LTYwLjhjMC04LjQtMS4xLTE2LjQtMy4xLTIzLjhMODY0IDczNnpNNTEyIDM1MmE0OC4wMSA0OC4wMSAwIDAxMCA5NiA0OC4wMSA0OC4wMSAwIDAxMC05NnptMTE2LjEgNDMyLjJjLTUuMiAzLTExLjIgNC4yLTE3LjEgMy40bC0xOS41LTIuNC0yLjggMTkuNGMtNS40IDM3LjktMzguNCA2Ni41LTc2LjcgNjYuNXMtNzEuMy0yOC42LTc2LjctNjYuNWwtMi44LTE5LjUtMTkuNSAyLjVhMjcuNyAyNy43IDAgMDEtMTcuMS0zLjVjLTguNy01LTE0LjEtMTQuMy0xNC4xLTI0LjQgMC0xMC42IDUuOS0xOS40IDE0LjYtMjMuOGgyMzEuM2M4LjggNC41IDE0LjYgMTMuMyAxNC42IDIzLjgtLjEgMTAuMi01LjUgMTkuNi0xNC4yIDI0LjV6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(RocketFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RocketFilled';
}
export default RefIcon;